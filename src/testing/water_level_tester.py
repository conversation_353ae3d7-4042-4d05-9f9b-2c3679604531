"""
水位识别测试器

专门用于测试水位识别算法的准确性和稳定性。
基于旧项目的水位识别代码进行适配。
"""

import cv2
import numpy as np
import logging
import sys
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple

# 添加旧项目路径以便导入
old_project_path = Path(__file__).parent.parent.parent / "old_src" / "water_level"
sys.path.insert(0, str(old_project_path))

try:
    # 尝试导入旧项目的核心函数
    from src.utils.water_level import calculate_water_depth, detect_water_surface
    from src.config.config_utils import ConfigManager as OldConfigManager
    OLD_PROJECT_AVAILABLE = True
except ImportError as e:
    logging.warning(f"无法导入旧项目代码: {e}")
    OLD_PROJECT_AVAILABLE = False

from .algorithm_tester import AlgorithmTester, TestResult

logger = logging.getLogger(__name__)


class WaterLevelTester(AlgorithmTester):
    """水位识别算法测试器"""
    
    def __init__(self, output_dir: Path):
        super().__init__("water_level", output_dir)
        self.old_config_manager = None
        
        # 如果旧项目可用，初始化旧的配置管理器
        if OLD_PROJECT_AVAILABLE:
            try:
                self.old_config_manager = OldConfigManager()
                logger.info("成功初始化旧项目配置管理器")
            except Exception as e:
                logger.warning(f"初始化旧项目配置管理器失败: {e}")
                self.old_config_manager = None
    
    def run_algorithm(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行水位识别算法
        
        Args:
            test_case: 测试用例数据
            
        Returns:
            Dict[str, Any]: 算法执行结果
        """
        image_path = test_case["image_path"]
        test_params = test_case["test_parameters"]
        
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        logger.info(f"开始分析图像: {image_path}")
        logger.info(f"图像尺寸: {image.shape}")
        
        # 如果旧项目可用，使用旧项目的算法
        if OLD_PROJECT_AVAILABLE and self.old_config_manager:
            return self._run_old_algorithm(image, test_params)
        else:
            # 使用简化的算法实现
            return self._run_simplified_algorithm(image, test_params)
    
    def _run_old_algorithm(self, image: np.ndarray, test_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用旧项目的算法
        
        Args:
            image: 输入图像
            test_params: 测试参数
            
        Returns:
            Dict[str, Any]: 算法结果
        """
        try:
            # 设置参数
            method = test_params.get("method", "transparency")
            roi = test_params.get("roi")
            brightness_thresh = test_params.get("brightness_thresh", 120)
            
            # 如果没有指定ROI，使用默认ROI或自动检测
            if roi is None:
                # 使用图像中心区域作为默认ROI
                h, w = image.shape[:2]
                roi = (w//4, h//4, w//2, h//2)  # 中心50%区域
            
            # 调用旧项目的水深计算函数
            result = calculate_water_depth(
                image=image,
                water_roi=roi,
                rule_roi=None,  # 让算法自动检测
                water_detection_method=method,
                brightness_thresh=brightness_thresh,
                debug=True
            )
            
            if result["success"]:
                return {
                    "success": True,
                    "water_depth_m": result["water_depth_cm"] / 100.0,  # 转换为米
                    "water_depth_cm": result["water_depth_cm"],
                    "water_surface_y": result["water_surface_y"],
                    "ruler_top_y": result["ruler_top"][1] if result["ruler_top"] else None,
                    "pixels_per_cm": result["pixels_per_cm"],
                    "exposed_length_cm": result["exposed_length_cm"],
                    "method_used": method,
                    "confidence": 0.9  # 旧算法没有置信度，给一个默认值
                }
            else:
                return {
                    "success": False,
                    "error": result["message"],
                    "water_depth_m": 0.0,
                    "method_used": method,
                    "confidence": 0.0
                }
                
        except Exception as e:
            logger.error(f"旧算法执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "water_depth_m": 0.0,
                "method_used": test_params.get("method", "unknown"),
                "confidence": 0.0
            }
    
    def _run_simplified_algorithm(self, image: np.ndarray, test_params: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用简化的算法实现（当旧项目不可用时）
        
        Args:
            image: 输入图像
            test_params: 测试参数
            
        Returns:
            Dict[str, Any]: 算法结果
        """
        try:
            method = test_params.get("method", "transparency")
            
            # 简化的水面检测（仅作为示例）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 使用简单的边缘检测来模拟水面检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 查找水平线
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, 
                                   minLineLength=100, maxLineGap=10)
            
            if lines is not None and len(lines) > 0:
                # 选择最长的水平线作为水面线
                horizontal_lines = []
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    if abs(y2 - y1) < 10:  # 近似水平线
                        horizontal_lines.append((y1 + y2) // 2)
                
                if horizontal_lines:
                    water_surface_y = int(np.median(horizontal_lines))
                    
                    # 简化的水深计算（假设标尺在图像顶部）
                    ruler_top_y = 0
                    exposed_length_pixels = water_surface_y - ruler_top_y
                    
                    # 假设的像素到厘米转换比例
                    pixels_per_cm = 10.0  # 假设值
                    exposed_length_cm = exposed_length_pixels / pixels_per_cm
                    
                    # 假设标尺总长度为5米
                    total_ruler_length_cm = 500
                    water_depth_cm = total_ruler_length_cm - exposed_length_cm
                    
                    return {
                        "success": True,
                        "water_depth_m": water_depth_cm / 100.0,
                        "water_depth_cm": water_depth_cm,
                        "water_surface_y": water_surface_y,
                        "ruler_top_y": ruler_top_y,
                        "pixels_per_cm": pixels_per_cm,
                        "exposed_length_cm": exposed_length_cm,
                        "method_used": method,
                        "confidence": 0.5  # 简化算法置信度较低
                    }
            
            # 如果没有检测到水面线
            return {
                "success": False,
                "error": "未检测到水面线",
                "water_depth_m": 0.0,
                "method_used": method,
                "confidence": 0.0
            }
            
        except Exception as e:
            logger.error(f"简化算法执行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "water_depth_m": 0.0,
                "method_used": test_params.get("method", "unknown"),
                "confidence": 0.0
            }
    
    def validate_results(self, actual: Dict[str, Any], expected: Dict[str, Any], 
                        tolerance: Dict[str, Any]) -> Tuple[bool, Dict[str, Any]]:
        """
        验证水位识别结果
        
        Args:
            actual: 实际结果
            expected: 期望结果
            tolerance: 容差配置
            
        Returns:
            Tuple[bool, Dict[str, Any]]: (是否通过, 误差详情)
        """
        if not actual.get("success", False):
            return False, {"error": "算法执行失败"}
        
        errors = {}
        within_tolerance = True
        
        # 检查水深误差
        if "water_depth_m" in expected:
            actual_depth = actual.get("water_depth_m", 0.0)
            expected_depth = expected["water_depth_m"]
            depth_error = abs(actual_depth - expected_depth)
            depth_tolerance = tolerance.get("water_depth_m", 0.1)
            
            errors["water_depth_m"] = actual_depth - expected_depth
            if depth_error > depth_tolerance:
                within_tolerance = False
        
        # 检查水面位置误差
        if "water_surface_y" in expected:
            actual_y = actual.get("water_surface_y", 0)
            expected_y = expected["water_surface_y"]
            y_error = abs(actual_y - expected_y)
            y_tolerance = tolerance.get("water_surface_y", 30)
            
            errors["water_surface_y"] = actual_y - expected_y
            if y_error > y_tolerance:
                within_tolerance = False
        
        # 检查标尺顶部位置误差
        if "ruler_top_y" in expected:
            actual_ruler_y = actual.get("ruler_top_y", 0)
            expected_ruler_y = expected["ruler_top_y"]
            if actual_ruler_y is not None:
                ruler_error = abs(actual_ruler_y - expected_ruler_y)
                ruler_tolerance = tolerance.get("ruler_top_y", 20)
                
                errors["ruler_top_y"] = actual_ruler_y - expected_ruler_y
                if ruler_error > ruler_tolerance:
                    within_tolerance = False
        
        return within_tolerance, errors

    def generate_visualization(self, test_case: Dict[str, Any],
                             result: TestResult) -> List[str]:
        """
        生成可视化结果

        Args:
            test_case: 测试用例数据
            result: 测试结果

        Returns:
            List[str]: 生成的文件路径列表
        """
        output_files = []

        try:
            # 读取原始图像
            image_path = test_case["image_path"]
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图像用于可视化: {image_path}")
                return output_files

            # 创建可视化图像
            vis_image = image.copy()

            # 获取结果数据
            actual_results = result.actual_results
            expected_results = result.expected_results

            # 绘制期望的水面线（绿色）
            if "water_surface_y" in expected_results:
                expected_y = expected_results["water_surface_y"]
                cv2.line(vis_image, (0, expected_y), (vis_image.shape[1], expected_y),
                        (0, 255, 0), 3)
                cv2.putText(vis_image, f"Expected Water Surface (y={expected_y})",
                           (10, expected_y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 绘制实际检测的水面线（蓝色）
            if actual_results.get("success") and "water_surface_y" in actual_results:
                actual_y = actual_results["water_surface_y"]
                cv2.line(vis_image, (0, actual_y), (vis_image.shape[1], actual_y),
                        (255, 0, 0), 2)
                cv2.putText(vis_image, f"Detected Water Surface (y={actual_y})",
                           (10, actual_y + 25), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            # 绘制期望的标尺顶部（绿色圆圈）
            if "ruler_top_y" in expected_results:
                expected_ruler_y = expected_results["ruler_top_y"]
                # 假设标尺在图像中央
                ruler_x = vis_image.shape[1] // 2
                cv2.circle(vis_image, (ruler_x, expected_ruler_y), 8, (0, 255, 0), -1)
                cv2.putText(vis_image, f"Expected Ruler Top (y={expected_ruler_y})",
                           (ruler_x + 15, expected_ruler_y - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

            # 绘制实际检测的标尺顶部（蓝色圆圈）
            if actual_results.get("success") and actual_results.get("ruler_top_y") is not None:
                actual_ruler_y = actual_results["ruler_top_y"]
                ruler_x = vis_image.shape[1] // 2
                cv2.circle(vis_image, (ruler_x, actual_ruler_y), 6, (255, 0, 0), -1)
                cv2.putText(vis_image, f"Detected Ruler Top (y={actual_ruler_y})",
                           (ruler_x + 15, actual_ruler_y + 25),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

            # 添加测试结果信息
            info_y = 30
            cv2.putText(vis_image, f"Test Case: {test_case['name']}",
                       (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (255, 255, 255), 2)

            info_y += 30
            status_color = (0, 255, 0) if result.status == "PASSED" else (0, 0, 255)
            cv2.putText(vis_image, f"Status: {result.status}",
                       (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.8, status_color, 2)

            if actual_results.get("success"):
                info_y += 30
                expected_depth = expected_results.get("water_depth_m", 0)
                actual_depth = actual_results.get("water_depth_m", 0)
                cv2.putText(vis_image, f"Expected Depth: {expected_depth:.2f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

                info_y += 25
                cv2.putText(vis_image, f"Actual Depth: {actual_depth:.2f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)

                info_y += 25
                depth_error = abs(actual_depth - expected_depth)
                cv2.putText(vis_image, f"Depth Error: {depth_error:.3f}m",
                           (10, info_y), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

            # 保存可视化结果
            vis_filename = f"{test_case['name']}_visualization.jpg"
            vis_path = self.output_dir / vis_filename
            cv2.imwrite(str(vis_path), vis_image)
            output_files.append(str(vis_path))

            logger.info(f"已生成可视化结果: {vis_path}")

        except Exception as e:
            logger.error(f"生成可视化失败: {str(e)}")

        return output_files
