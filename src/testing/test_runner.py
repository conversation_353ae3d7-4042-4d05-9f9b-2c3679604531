"""
测试运行器

主要的测试执行入口，负责协调整个测试流程。
"""

import argparse
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from .test_data_manager import TestDataManager
from .water_level_tester import WaterLevelTester
from .report_generator import ReportGenerator

logger = logging.getLogger(__name__)


class TestRunner:
    """测试运行器"""
    
    def __init__(self, workspace_root: Path):
        """
        初始化测试运行器
        
        Args:
            workspace_root: 工作空间根目录
        """
        self.workspace_root = workspace_root
        self.test_cases_dir = workspace_root / "data" / "test_cases"
        self.test_results_dir = workspace_root / "data" / "test_results"
        
        # 确保目录存在
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.test_results_dir.mkdir(parents=True, exist_ok=True)
        
        # 初始化组件
        self.data_manager = TestDataManager(self.test_cases_dir)
        self.report_generator = ReportGenerator(self.test_results_dir)
        
        # 支持的算法测试器
        self.testers = {
            "water_level": lambda: WaterLevelTester(self.test_results_dir)
        }
        
        logger.info(f"测试运行器已初始化，工作目录: {workspace_root}")
    
    def run_algorithm_tests(self, algorithm: str, case_names: Optional[List[str]] = None,
                           generate_report: bool = True, report_format: str = "html") -> Dict[str, Any]:
        """
        运行指定算法的测试
        
        Args:
            algorithm: 算法名称
            case_names: 要运行的测试用例名称列表，None表示运行所有
            generate_report: 是否生成测试报告
            report_format: 报告格式 ("html", "yaml", "json")
            
        Returns:
            Dict[str, Any]: 测试结果摘要
        """
        if algorithm not in self.testers:
            raise ValueError(f"不支持的算法: {algorithm}，支持的算法: {list(self.testers.keys())}")
        
        logger.info(f"开始运行 {algorithm} 算法测试")
        
        # 加载测试用例
        test_data = self.data_manager.load_test_cases(algorithm)
        if not test_data.get("test_cases"):
            logger.warning(f"没有找到 {algorithm} 的测试用例，尝试创建默认测试用例")
            self.data_manager.create_default_test_cases(algorithm)
            test_data = self.data_manager.load_test_cases(algorithm)
        
        test_cases = test_data.get("test_cases", [])
        if not test_cases:
            raise RuntimeError(f"没有可用的 {algorithm} 测试用例")
        
        # 过滤测试用例
        filtered_cases = self.data_manager.filter_test_cases(test_cases, case_names)
        if not filtered_cases:
            raise RuntimeError(f"没有匹配的测试用例: {case_names}")
        
        logger.info(f"将运行 {len(filtered_cases)} 个测试用例")
        
        # 验证测试用例
        valid_cases = []
        for test_case in filtered_cases:
            if self.data_manager.validate_test_case(test_case):
                valid_cases.append(test_case)
            else:
                logger.warning(f"跳过无效的测试用例: {test_case.get('name', 'unknown')}")
        
        if not valid_cases:
            raise RuntimeError("没有有效的测试用例")
        
        # 创建测试器并运行测试
        tester = self.testers[algorithm]()
        results = tester.run_test_cases(valid_cases)
        
        # 转换结果为字典格式
        result_dicts = [result.to_dict() for result in results]
        
        # 保存测试结果
        result_file = self.data_manager.save_test_results(algorithm, result_dicts, self.test_results_dir)
        
        # 生成测试摘要
        summary = self._generate_summary(algorithm, results)
        
        # 生成报告
        if generate_report:
            report_path = self.report_generator.generate_report(
                algorithm, result_dicts, summary, report_format
            )
            summary["report_path"] = str(report_path)
        
        summary["result_file"] = str(result_file)
        
        logger.info(f"{algorithm} 算法测试完成")
        return summary
    
    def run_all_tests(self, generate_report: bool = True, report_format: str = "html") -> Dict[str, Any]:
        """
        运行所有算法的测试
        
        Args:
            generate_report: 是否生成测试报告
            report_format: 报告格式
            
        Returns:
            Dict[str, Any]: 所有测试结果摘要
        """
        logger.info("开始运行所有算法测试")
        
        all_results = {}
        total_passed = 0
        total_failed = 0
        total_errors = 0
        
        for algorithm in self.testers.keys():
            try:
                result = self.run_algorithm_tests(algorithm, generate_report=False)
                all_results[algorithm] = result
                
                total_passed += result["passed_cases"]
                total_failed += result["failed_cases"]
                total_errors += result["error_cases"]
                
            except Exception as e:
                logger.error(f"算法 {algorithm} 测试失败: {str(e)}")
                all_results[algorithm] = {
                    "status": "ERROR",
                    "error": str(e),
                    "total_cases": 0,
                    "passed_cases": 0,
                    "failed_cases": 0,
                    "error_cases": 1
                }
                total_errors += 1
        
        # 生成总体摘要
        total_cases = total_passed + total_failed + total_errors
        overall_summary = {
            "timestamp": datetime.now().isoformat(),
            "total_algorithms": len(self.testers),
            "total_cases": total_cases,
            "passed_cases": total_passed,
            "failed_cases": total_failed,
            "error_cases": total_errors,
            "success_rate": (total_passed / total_cases * 100) if total_cases > 0 else 0,
            "algorithm_results": all_results
        }
        
        # 生成综合报告
        if generate_report:
            report_path = self.report_generator.generate_comprehensive_report(
                all_results, overall_summary, report_format
            )
            overall_summary["report_path"] = str(report_path)
        
        logger.info("所有算法测试完成")
        return overall_summary
    
    def _generate_summary(self, algorithm: str, results: List) -> Dict[str, Any]:
        """
        生成测试摘要
        
        Args:
            algorithm: 算法名称
            results: 测试结果列表
            
        Returns:
            Dict[str, Any]: 测试摘要
        """
        total_cases = len(results)
        passed_cases = sum(1 for r in results if r.status == "PASSED")
        failed_cases = sum(1 for r in results if r.status == "FAILED")
        error_cases = sum(1 for r in results if r.status == "ERROR")
        
        success_rate = (passed_cases / total_cases * 100) if total_cases > 0 else 0
        
        # 计算平均执行时间
        execution_times = [r.execution_time for r in results if r.execution_time > 0]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        return {
            "timestamp": datetime.now().isoformat(),
            "algorithm": algorithm,
            "total_cases": total_cases,
            "passed_cases": passed_cases,
            "failed_cases": failed_cases,
            "error_cases": error_cases,
            "success_rate": round(success_rate, 2),
            "avg_execution_time": round(avg_execution_time, 2),
            "status": "SUCCESS" if error_cases == 0 and failed_cases == 0 else "PARTIAL" if passed_cases > 0 else "FAILED"
        }
    
    def list_available_algorithms(self) -> List[str]:
        """
        获取支持的算法列表
        
        Returns:
            List[str]: 算法名称列表
        """
        return list(self.testers.keys())
    
    def list_test_cases(self, algorithm: str) -> List[str]:
        """
        获取指定算法的测试用例列表
        
        Args:
            algorithm: 算法名称
            
        Returns:
            List[str]: 测试用例名称列表
        """
        test_data = self.data_manager.load_test_cases(algorithm)
        test_cases = test_data.get("test_cases", [])
        return [case.get("name", "unknown") for case in test_cases]


def setup_logging(debug: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_runner.log', encoding='utf-8')
        ]
    )


def main():
    """命令行入口函数"""
    parser = argparse.ArgumentParser(description="VR算法测试运行器")
    parser.add_argument("--algorithm", "-a", help="要测试的算法名称 (water_level, flow_speed, anomaly)")
    parser.add_argument("--case", "-c", action="append", help="要运行的测试用例名称（可多次指定）")
    parser.add_argument("--all", action="store_true", help="运行所有算法的测试")
    parser.add_argument("--list-algorithms", action="store_true", help="列出支持的算法")
    parser.add_argument("--list-cases", help="列出指定算法的测试用例")
    parser.add_argument("--report-format", choices=["html", "yaml", "json"], default="html",
                       help="报告格式")
    parser.add_argument("--no-report", action="store_true", help="不生成测试报告")
    parser.add_argument("--debug", action="store_true", help="启用调试模式")
    parser.add_argument("--workspace", default=".", help="工作空间根目录")

    args = parser.parse_args()

    # 设置日志
    setup_logging(args.debug)

    try:
        # 初始化测试运行器
        workspace_root = Path(args.workspace).resolve()
        runner = TestRunner(workspace_root)

        # 处理命令
        if args.list_algorithms:
            algorithms = runner.list_available_algorithms()
            print("支持的算法:")
            for algo in algorithms:
                print(f"  - {algo}")
            return

        if args.list_cases:
            try:
                cases = runner.list_test_cases(args.list_cases)
                print(f"{args.list_cases} 算法的测试用例:")
                for case in cases:
                    print(f"  - {case}")
            except Exception as e:
                print(f"获取测试用例失败: {e}")
                sys.exit(1)
            return

        if args.all:
            # 运行所有算法测试
            print("开始运行所有算法测试...")
            result = runner.run_all_tests(
                generate_report=not args.no_report,
                report_format=args.report_format
            )

            print(f"\n=== 测试完成 ===")
            print(f"总算法数: {result['total_algorithms']}")
            print(f"总测试用例: {result['total_cases']}")
            print(f"通过: {result['passed_cases']}")
            print(f"失败: {result['failed_cases']}")
            print(f"错误: {result['error_cases']}")
            print(f"总成功率: {result['success_rate']:.1f}%")

            if result.get('report_path'):
                print(f"测试报告: {result['report_path']}")

        elif args.algorithm:
            # 运行指定算法测试
            print(f"开始运行 {args.algorithm} 算法测试...")
            result = runner.run_algorithm_tests(
                algorithm=args.algorithm,
                case_names=args.case,
                generate_report=not args.no_report,
                report_format=args.report_format
            )

            print(f"\n=== {args.algorithm} 测试完成 ===")
            print(f"测试用例: {result['total_cases']}")
            print(f"通过: {result['passed_cases']}")
            print(f"失败: {result['failed_cases']}")
            print(f"错误: {result['error_cases']}")
            print(f"成功率: {result['success_rate']:.1f}%")
            print(f"平均执行时间: {result['avg_execution_time']:.2f} 秒")
            print(f"状态: {result['status']}")

            if result.get('report_path'):
                print(f"测试报告: {result['report_path']}")

            print(f"详细结果: {result['result_file']}")

        else:
            parser.print_help()
            print("\n示例用法:")
            print("  python -m src.testing.test_runner --algorithm water_level")
            print("  python -m src.testing.test_runner --algorithm water_level --case water_level_case_1")
            print("  python -m src.testing.test_runner --all")
            print("  python -m src.testing.test_runner --list-algorithms")
            print("  python -m src.testing.test_runner --list-cases water_level")

    except Exception as e:
        logger.error(f"测试运行失败: {str(e)}")
        if args.debug:
            import traceback
            traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
