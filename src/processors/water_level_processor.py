"""
水位识别处理器

整合所有水位识别算法，提供统一的水位分析接口。
支持透明度、梯度、霍夫变换、颜色阈值等多种检测方法。
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

try:
    from ..utils.config_manager import ConfigManager
    from ..utils.video_utils import VideoStreamProcessor
    from ..utils.image_utils import ImageProcessor
    from ..algorithms.water_level.transparency import detect_waterline_transparency
    from ..algorithms.water_level.gradient import detect_waterline_gradient
    from ..algorithms.water_level.hough import detect_waterline_hough
    from ..algorithms.water_level.color_threshold import detect_waterline_color_threshold
except ImportError:
    # 回退到绝对导入
    from utils.config_manager import ConfigManager
    from utils.video_utils import VideoStreamProcessor
    from utils.image_utils import ImageProcessor
    from algorithms.water_level.transparency import detect_waterline_transparency
    from algorithms.water_level.gradient import detect_waterline_gradient
    from algorithms.water_level.hough import detect_waterline_hough
    from algorithms.water_level.color_threshold import detect_waterline_color_threshold


logger = logging.getLogger(__name__)


class WaterLevelProcessor:
    """
    水位识别处理器
    
    整合多种水位检测算法，提供统一的分析接口
    """
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化水位识别处理器
        
        Args:
            config_manager: 配置管理器实例
        """
        self.config_manager = config_manager
        self.logger = logger
        
        # 初始化工具组件
        self.video_processor = VideoStreamProcessor(config_manager)
        self.image_processor = ImageProcessor(config_manager)
        
        # 获取配置
        self.default_method = config_manager.get("analyzers.water_level.default_method", "transparency")
        self.max_analysis_duration = config_manager.get("analyzers.water_level.max_analysis_duration", 300)
        
        # 支持的检测方法
        self.available_methods = {
            "transparency": detect_waterline_transparency,
            "gradient": detect_waterline_gradient,
            "hough": detect_waterline_hough,
            "color_threshold": detect_waterline_color_threshold
        }
        
        self.logger.info(f"水位识别处理器已初始化，支持方法: {list(self.available_methods.keys())}")
    
    async def analyze(
        self,
        video_url: str,
        method: str = None,
        duration: int = 30,
        roi: Optional[Dict] = None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        执行水位识别分析

        Args:
            video_url: 视频流URL
            method: 检测方法
            duration: 分析时长(秒)
            roi: 感兴趣区域 {"x": int, "y": int, "width": int, "height": int}
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 分析结果
        """
        start_time = time.time()
        self.logger.debug(f"水位分析任务开始: {task_id}")

        try:
            # 参数验证
            method = method or self.default_method
            duration = min(duration, self.max_analysis_duration)

            self.logger.debug(f"分析参数: method={method}, duration={duration}, roi={roi}")

            if method not in self.available_methods:
                self.logger.error(f"不支持的检测方法: {method}")
                raise ValueError(f"不支持的检测方法: {method}，支持的方法: {list(self.available_methods.keys())}")

            self.logger.info(f"开始水位分析: 方法={method}, 时长={duration}秒, 任务ID={task_id}")

            # 连接视频流
            self.logger.debug(f"正在连接视频源: {video_url}")
            if not await self.video_processor.connect(video_url):
                self.logger.error(f"视频源连接失败: {video_url}")
                raise RuntimeError(f"无法连接到视频源: {video_url}")

            try:
                # 获取视频信息
                video_info = self.video_processor.get_video_info()
                self.logger.info(f"视频信息: {video_info}")
                self.logger.debug(f"视频尺寸: {video_info.get('width')}x{video_info.get('height')}, FPS: {video_info.get('fps')}")

                # 收集分析帧
                self.logger.debug(f"开始提取视频帧，时长: {duration}秒")
                analysis_frames = []
                frame_count = 0
                async for frame in self.video_processor.extract_frames(duration, roi):
                    analysis_frames.append(frame)
                    frame_count += 1

                    # 每10帧记录一次进度
                    if frame_count % 10 == 0:
                        self.logger.debug(f"已提取 {frame_count} 帧")

                    # 限制帧数以控制内存使用
                    if len(analysis_frames) >= 100:  # 最多分析100帧
                        self.logger.debug(f"达到最大帧数限制(100帧)，停止提取")
                        break

                if not analysis_frames:
                    self.logger.error("未能提取到有效的视频帧")
                    raise RuntimeError("未能提取到有效的视频帧")

                self.logger.info(f"提取了{len(analysis_frames)}帧用于分析")

                # 执行水位检测
                self.logger.debug(f"开始执行水位检测算法: {method}")
                result = await self._analyze_frames(analysis_frames, method, roi, task_id)

                # 计算处理时间
                processing_time = time.time() - start_time
                result["processing_time"] = processing_time

                self.logger.info(f"水位分析完成: 任务ID={task_id}, 耗时={processing_time:.2f}秒")
                self.logger.debug(f"分析结果: {result}")

                return result

            finally:
                # 断开视频连接
                self.logger.debug("断开视频连接")
                self.video_processor.disconnect()

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"水位分析失败: 任务ID={task_id}, 错误={str(e)}, 耗时={processing_time:.2f}秒")
            raise
    
    async def _analyze_frames(
        self,
        frames: List,
        method: str,
        roi: Optional[Dict],
        task_id: str
    ) -> Dict[str, Any]:
        """
        分析视频帧序列，使用完整的水深计算流程

        Args:
            frames: 视频帧列表
            method: 检测方法
            roi: ROI区域
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 分析结果
        """
        try:
            water_depths = []
            valid_detections = 0

            for frame_idx, frame in enumerate(frames):
                try:
                    # 使用完整的水深计算流程
                    depth_result = await self._calculate_water_depth_for_frame(
                        frame, method, roi, task_id, frame_idx
                    )

                    if depth_result.get("success", False):
                        water_depths.append(depth_result["water_depth_cm"])
                        valid_detections += 1

                        self.logger.debug(f"帧{frame_idx}: 检测到水深 {depth_result['water_depth_cm']:.2f}cm")
                    else:
                        self.logger.debug(f"帧{frame_idx}: 未检测到水位线")
                        
                except Exception as e:
                    self.logger.warning(f"帧{frame_idx}分析失败: {str(e)}")
                    continue
            
            # 分析结果
            if not water_depths:
                return {
                    "success": False,
                    "error": "所有帧都未检测到水位线",
                    "water_depth_cm": 0.0,
                    "confidence": 0.0,
                    "method_used": method,
                    "frames_analyzed": len(frames),
                    "valid_detections": 0
                }
            
            # 计算统计值
            import numpy as np
            avg_water_depth_cm = float(np.mean(water_depths))
            std_water_depth_cm = float(np.std(water_depths))

            # 计算置信度（基于检测成功率和结果稳定性）
            detection_rate = valid_detections / len(frames)
            stability_factor = max(0.1, 1.0 - (std_water_depth_cm / avg_water_depth_cm if avg_water_depth_cm > 0 else 1.0))
            confidence = min(1.0, detection_rate * stability_factor)
            
            return {
                "success": True,
                "water_depth_cm": avg_water_depth_cm,
                "confidence": confidence,
                "method_used": method,
                "frames_analyzed": len(frames),
                "valid_detections": valid_detections,
                "avg_water_depth_cm": avg_water_depth_cm,
                "std_water_depth_cm": std_water_depth_cm,
                "detection_rate": detection_rate,
                "pixels_per_cm": None  # 需要标定数据
            }
            
        except Exception as e:
            self.logger.error(f"帧序列分析失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "water_depth_cm": 0.0,
                "confidence": 0.0,
                "method_used": method,
                "frames_analyzed": len(frames),
                "valid_detections": 0
            }
    
    def _apply_roi(self, frame, roi: Dict):
        """
        应用ROI裁剪
        
        Args:
            frame: 输入帧
            roi: ROI参数
            
        Returns:
            裁剪后的帧
        """
        try:
            x = roi.get("x", 0)
            y = roi.get("y", 0)
            width = roi.get("width", frame.shape[1])
            height = roi.get("height", frame.shape[0])
            
            # 确保ROI在图像范围内
            x = max(0, min(x, frame.shape[1] - 1))
            y = max(0, min(y, frame.shape[0] - 1))
            width = min(width, frame.shape[1] - x)
            height = min(height, frame.shape[0] - y)
            
            return frame[y:y+height, x:x+width]
            
        except Exception as e:
            self.logger.error(f"ROI应用失败: {str(e)}")
            return frame
    
    def get_supported_methods(self) -> List[str]:
        """
        获取支持的检测方法列表
        
        Returns:
            List[str]: 支持的方法列表
        """
        return list(self.available_methods.keys())
    
    def get_method_info(self, method: str) -> Dict[str, Any]:
        """
        获取检测方法的详细信息
        
        Args:
            method: 方法名称
            
        Returns:
            Dict[str, Any]: 方法信息
        """
        method_descriptions = {
            "transparency": {
                "name": "透明度检测",
                "description": "基于水面透明度特性检测水位线",
                "suitable_for": "清澈水体，光照条件良好",
                "parameters": ["alpha_thresh", "canny_low", "canny_high", "blur_ksize"]
            },
            "gradient": {
                "name": "梯度检测", 
                "description": "基于像素强度梯度变化检测水位线",
                "suitable_for": "水面与背景对比明显",
                "parameters": ["grad_thresh", "blur_kernel_size"]
            },
            "hough": {
                "name": "霍夫变换检测",
                "description": "基于霍夫直线变换检测水位线",
                "suitable_for": "水面线条清晰，噪声较少",
                "parameters": ["thresh", "min_line_len_factor", "max_gap"]
            },
            "color_threshold": {
                "name": "颜色阈值检测",
                "description": "基于HSV颜色空间分析检测水位线",
                "suitable_for": "水体颜色特征明显",
                "parameters": ["hsv_lower", "hsv_upper", "morph_kernel_size"]
            }
        }
        
        return method_descriptions.get(method, {"name": "未知方法", "description": "方法信息不可用"})
    
    async def analyze_image(
        self,
        image_path: str,
        method: str = None,
        roi: Optional[Dict] = None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        分析单张图片的水位

        Args:
            image_path: 图片路径
            method: 检测方法
            roi: 感兴趣区域
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 分析结果
        """
        import cv2
        import time

        start_time = time.time()
        self.logger.debug(f"图片水位分析任务开始: {task_id}")

        try:
            # 参数验证
            method = method or self.default_method

            if method not in self.available_methods:
                self.logger.error(f"不支持的检测方法: {method}")
                raise ValueError(f"不支持的检测方法: {method}，支持的方法: {list(self.available_methods.keys())}")

            # 读取图片
            image = cv2.imread(image_path)
            if image is None:
                self.logger.error(f"无法读取图片: {image_path}")
                raise ValueError(f"无法读取图片: {image_path}")

            self.logger.info(f"开始图片水位分析: 方法={method}, 图片={image_path}, 任务ID={task_id}")
            self.logger.debug(f"图片尺寸: {image.shape}")

            # 使用现有的帧分析方法
            result = await self._analyze_frames([image], method, roi, task_id)

            # 计算处理时间
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            result["image_path"] = image_path
            result["image_shape"] = image.shape

            self.logger.info(f"图片水位分析完成: 任务ID={task_id}, 耗时={processing_time:.2f}秒")
            self.logger.debug(f"分析结果: {result}")

            return result

        except Exception as e:
            processing_time = time.time() - start_time
            self.logger.error(f"图片水位分析失败: 任务ID={task_id}, 错误={str(e)}, 耗时={processing_time:.2f}秒")
            raise

    def analyze_image_sync(
        self,
        image_path: str,
        method: str = None,
        roi: Optional[Dict] = None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """
        同步版本的图片水位分析

        Args:
            image_path: 图片路径
            method: 检测方法
            roi: 感兴趣区域
            task_id: 任务ID

        Returns:
            Dict[str, Any]: 分析结果
        """
        import asyncio

        # 如果在异步环境中，直接调用异步方法
        try:
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 在已有的事件循环中，创建新任务
                return asyncio.create_task(self.analyze_image(image_path, method, roi, task_id))
            else:
                # 运行新的事件循环
                return loop.run_until_complete(self.analyze_image(image_path, method, roi, task_id))
        except RuntimeError:
            # 没有事件循环，创建新的
            return asyncio.run(self.analyze_image(image_path, method, roi, task_id))

    async def _calculate_water_depth_for_frame(
        self,
        frame,
        method: str,
        roi: Optional[Dict],
        task_id: str,
        frame_idx: int
    ) -> Dict[str, Any]:
        """
        为单个帧计算水深，使用完整的水深计算流程

        Args:
            frame: 图像帧
            method: 检测方法
            roi: ROI区域
            task_id: 任务ID
            frame_idx: 帧索引

        Returns:
            Dict[str, Any]: 水深计算结果
        """
        try:
            # 步骤1: 检测水尺并获取所有E字母信息
            ruler_detection = await self._detect_water_ruler(frame)

            if not ruler_detection.get("success", False):
                return {
                    "success": False,
                    "error": "未能成功检测到水尺",
                    "water_depth_cm": 0.0
                }

            # 获取水尺顶部坐标和所有E字母信息
            ruler_top = ruler_detection["ruler_top"]
            all_e_letters = ruler_detection["all_e_letters"]

            # 步骤2: 如果检测到了水尺ROI，更新ROI
            detected_roi = ruler_detection.get("roi")
            if detected_roi:
                self.logger.debug(f"检测到水尺ROI: {detected_roi}")
                # 使用检测到的ROI作为水面检测的ROI
                water_roi = detected_roi
            else:
                # 使用原始ROI或默认ROI
                water_roi = roi or {"x": 0, "y": 0, "width": frame.shape[1], "height": frame.shape[0]}

            # 步骤3: 检测水面位置
            water_surface_y = await self._detect_water_surface_for_frame(
                frame, water_roi, method
            )

            if water_surface_y is None:
                return {
                    "success": False,
                    "error": "未能检测到水面位置",
                    "water_depth_cm": 0.0
                }

            # 步骤4: 计算像素/厘米比率
            pixels_per_cm = self._calculate_pixel_cm_ratio(all_e_letters, water_surface_y)

            if pixels_per_cm is None or pixels_per_cm <= 0:
                return {
                    "success": False,
                    "error": "无法计算有效的像素/厘米比率",
                    "water_depth_cm": 0.0
                }

            # 步骤5: 计算水面到水尺顶部的像素距离（露出水面的水尺长度）
            exposed_length_pixels = water_surface_y - ruler_top[1]

            # 步骤6: 将像素距离转换为厘米
            exposed_length_cm = exposed_length_pixels / pixels_per_cm

            # 步骤7: 计算实际水深 = 水尺总长度 - 露出水面的长度
            total_ruler_length_cm = self.config_manager.get_config("analyzers.water_level.methods.ruler_detection.length", 5) * 100
            water_depth_cm = total_ruler_length_cm - exposed_length_cm

            return {
                "success": True,
                "water_depth_cm": water_depth_cm,
                "water_surface_y": water_surface_y,
                "ruler_top": ruler_top,
                "pixels_per_cm": pixels_per_cm,
                "exposed_length_pixels": exposed_length_pixels,
                "exposed_length_cm": exposed_length_cm,
                "total_ruler_length_cm": total_ruler_length_cm
            }

        except Exception as e:
            self.logger.error(f"帧{frame_idx}水深计算失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "water_depth_cm": 0.0
            }

    async def _detect_water_ruler(self, frame) -> Dict[str, Any]:
        """
        检测水尺并获取E字母信息

        Args:
            frame: 图像帧

        Returns:
            Dict[str, Any]: 水尺检测结果
        """
        try:
            # 这里需要实现水尺检测逻辑
            # 暂时返回模拟结果，实际需要集成旧项目的detect_water_rule函数
            self.logger.warning("水尺检测功能尚未完全实现，返回模拟结果")

            # 模拟结果
            height, width = frame.shape[:2]
            return {
                "success": False,  # 暂时设为False，因为还未实现
                "ruler_top": (width // 2, height // 4),  # 模拟位置
                "all_e_letters": [],  # 空的E字母列表
                "roi": None
            }

        except Exception as e:
            self.logger.error(f"水尺检测失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _detect_water_surface_for_frame(self, frame, roi: Dict, method: str) -> Optional[int]:
        """
        为单个帧检测水面位置

        Args:
            frame: 图像帧
            roi: ROI区域
            method: 检测方法

        Returns:
            Optional[int]: 水面Y坐标，如果未检测到则返回None
        """
        try:
            # 转换ROI格式
            roi_tuple = (roi.get("x", 0), roi.get("y", 0), roi.get("width", frame.shape[1]), roi.get("height", frame.shape[0]))

            # 调用现有的水面检测算法
            detection_function = self.available_methods[method]

            # 应用ROI裁剪
            roi_frame = self._apply_roi(frame, roi)

            # 根据方法选择预处理方式
            if method in ["transparency", "color_threshold"]:
                processed_frame = roi_frame
            else:
                processed_frame = self.image_processor.preprocess_roi_for_waterline(roi_frame)

            # 执行检测
            waterline_y = detection_function(
                processed_frame,
                config_manager=self.config_manager,
                debug=False
            )

            # 转换为全局坐标
            if waterline_y is not None:
                return waterline_y + roi.get("y", 0)
            else:
                return None

        except Exception as e:
            self.logger.error(f"水面检测失败: {str(e)}")
            return None

    def _calculate_pixel_cm_ratio(self, all_e_letters: list, water_surface_y: int) -> Optional[float]:
        """
        计算像素/厘米比率

        Args:
            all_e_letters: 所有E字母信息
            water_surface_y: 水面Y坐标

        Returns:
            Optional[float]: 像素/厘米比率，如果无法计算则返回None
        """
        try:
            if not all_e_letters:
                self.logger.warning("没有E字母信息，无法计算像素/厘米比率")
                return None

            # 这里需要实现像素比率计算逻辑
            # 暂时返回一个默认值，实际需要集成旧项目的calculate_pixel_cm_ratio函数
            self.logger.warning("像素比率计算功能尚未完全实现，使用默认值")
            return 10.0  # 默认10像素/厘米

        except Exception as e:
            self.logger.error(f"像素比率计算失败: {str(e)}")
            return None

    async def cleanup(self):
        """清理资源"""
        try:
            if self.video_processor:
                self.video_processor.disconnect()
            self.logger.info("水位识别处理器资源清理完成")
        except Exception as e:
            self.logger.error(f"水位识别处理器资源清理失败: {str(e)}")
