#!/usr/bin/env python3
"""
测试框架验证脚本

用于验证测试框架是否正常工作。
"""

import sys
import logging
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

def test_framework():
    """测试框架基本功能"""
    print("=== VR算法测试框架验证 ===\n")
    
    try:
        # 导入测试组件
        from src.testing.test_runner import TestRunner
        from src.testing.test_data_manager import TestDataManager
        from src.testing.water_level_tester import WaterLevelTester
        
        print("✓ 成功导入测试框架组件")
        
        # 初始化测试运行器
        workspace_root = Path(".").resolve()
        runner = TestRunner(workspace_root)
        print("✓ 成功初始化测试运行器")
        
        # 检查支持的算法
        algorithms = runner.list_available_algorithms()
        print(f"✓ 支持的算法: {algorithms}")
        
        # 检查测试用例
        if "water_level" in algorithms:
            cases = runner.list_test_cases("water_level")
            print(f"✓ 水位识别测试用例: {cases}")
            
            # 检查测试图片是否存在
            test_images = [
                "data/temp/water_level.jpg",
                "data/temp/water_level2.jpg"
            ]
            
            for img_path in test_images:
                if Path(img_path).exists():
                    print(f"✓ 测试图片存在: {img_path}")
                else:
                    print(f"✗ 测试图片不存在: {img_path}")
        
        print("\n=== 框架验证完成 ===")
        print("可以使用以下命令运行测试:")
        print("  python main.py --test --algorithm water_level")
        print("  python main.py --list-algorithms")
        print("  python main.py --list-cases water_level")
        
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        print("请检查依赖是否正确安装")
        return False
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False
    
    return True

def check_dependencies():
    """检查依赖"""
    print("=== 检查依赖 ===")
    
    required_packages = [
        "cv2", "numpy", "yaml", "pathlib"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package} (缺失)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {missing_packages}")
        print("请安装缺失的依赖包")
        return False
    
    print("✓ 所有依赖都已安装")
    return True

def check_test_data():
    """检查测试数据"""
    print("\n=== 检查测试数据 ===")
    
    # 检查测试图片
    test_images = [
        "data/temp/water_level.jpg",
        "data/temp/water_level2.jpg"
    ]
    
    for img_path in test_images:
        if Path(img_path).exists():
            print(f"✓ {img_path}")
        else:
            print(f"✗ {img_path} (不存在)")
    
    # 检查测试用例配置
    test_config = "data/test_cases/water_level_test_cases.yaml"
    if Path(test_config).exists():
        print(f"✓ {test_config}")
    else:
        print(f"✗ {test_config} (不存在)")
    
    # 检查输出目录
    output_dirs = [
        "data/test_results",
        "data/test_cases"
    ]
    
    for dir_path in output_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✓ {dir_path} (已创建)")

def main():
    """主函数"""
    setup_logging()
    
    print("VR算法测试框架验证工具\n")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查测试数据
    check_test_data()
    
    # 测试框架
    if test_framework():
        print("\n🎉 测试框架验证成功！")
        print("\n下一步:")
        print("1. 确保测试图片存在于 data/temp/ 目录")
        print("2. 运行测试: python main.py --test --algorithm water_level")
        print("3. 查看测试报告: data/test_results/")
    else:
        print("\n❌ 测试框架验证失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
