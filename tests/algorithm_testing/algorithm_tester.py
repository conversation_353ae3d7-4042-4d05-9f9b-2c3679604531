"""
算法测试器基类

定义所有算法测试器的标准接口和通用方法。
"""

import time
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class TestResult:
    """测试结果数据类"""
    
    def __init__(self, case_name: str):
        self.case_name = case_name
        self.status = "PENDING"  # PENDING, RUNNING, PASSED, FAILED, ERROR
        self.execution_time = 0.0
        self.actual_results = {}
        self.expected_results = {}
        self.errors = {}
        self.within_tolerance = False
        self.error_message = None
        self.output_files = []
        
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "case_name": self.case_name,
            "status": self.status,
            "execution_time": self.execution_time,
            "actual_results": self.actual_results,
            "expected_results": self.expected_results,
            "errors": self.errors,
            "within_tolerance": self.within_tolerance,
            "error_message": self.error_message,
            "output_files": self.output_files
        }


class AlgorithmTester(ABC):
    """算法测试器基类"""
    
    def __init__(self, algorithm_name: str, output_dir: Path):
        self.algorithm_name = algorithm_name
        self.output_dir = output_dir
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    @abstractmethod
    def run_algorithm(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """
        运行算法
        
        Args:
            test_case: 测试用例数据
            
        Returns:
            Dict[str, Any]: 算法执行结果
        """
        pass
    
    @abstractmethod
    def validate_results(self, actual: Dict[str, Any], expected: Dict[str, Any], 
                        tolerance: Dict[str, Any]) -> tuple[bool, Dict[str, Any]]:
        """
        验证结果
        
        Args:
            actual: 实际结果
            expected: 期望结果
            tolerance: 容差配置
            
        Returns:
            tuple[bool, Dict[str, Any]]: (是否通过, 误差详情)
        """
        pass
    
    @abstractmethod
    def generate_visualization(self, test_case: Dict[str, Any], 
                             result: TestResult) -> List[str]:
        """
        生成可视化结果
        
        Args:
            test_case: 测试用例数据
            result: 测试结果
            
        Returns:
            List[str]: 生成的文件路径列表
        """
        pass
    
    def run_test_case(self, test_case: Dict[str, Any]) -> TestResult:
        """
        运行单个测试用例
        
        Args:
            test_case: 测试用例数据
            
        Returns:
            TestResult: 测试结果
        """
        result = TestResult(test_case["name"])
        result.expected_results = test_case["expected_results"]
        
        try:
            logger.info(f"开始运行测试用例: {test_case['name']}")
            result.status = "RUNNING"
            
            # 记录开始时间
            start_time = time.time()
            
            # 运行算法
            actual_results = self.run_algorithm(test_case)
            result.actual_results = actual_results
            
            # 记录执行时间
            result.execution_time = time.time() - start_time
            
            # 验证结果
            tolerance = test_case["expected_results"].get("tolerance", {})
            within_tolerance, errors = self.validate_results(
                actual_results, 
                test_case["expected_results"], 
                tolerance
            )
            
            result.within_tolerance = within_tolerance
            result.errors = errors
            result.status = "PASSED" if within_tolerance else "FAILED"
            
            # 生成可视化
            output_files = self.generate_visualization(test_case, result)
            result.output_files = output_files
            
            logger.info(f"测试用例 {test_case['name']} 完成: {result.status}")
            
        except Exception as e:
            result.status = "ERROR"
            result.error_message = str(e)
            logger.error(f"测试用例 {test_case['name']} 执行出错: {str(e)}", exc_info=True)
            
        return result
    
    def run_test_cases(self, test_cases: List[Dict[str, Any]]) -> List[TestResult]:
        """
        运行多个测试用例
        
        Args:
            test_cases: 测试用例列表
            
        Returns:
            List[TestResult]: 测试结果列表
        """
        results = []
        
        for test_case in test_cases:
            result = self.run_test_case(test_case)
            results.append(result)
            
        return results
