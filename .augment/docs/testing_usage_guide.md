# VR算法测试框架使用指南

本指南详细介绍如何使用VR算法测试框架进行算法测试和验证。

## 快速开始

### 1. 验证测试框架

首先运行验证脚本确保测试框架正常工作：

```bash
python test_framework.py
```

预期输出：
```
✓ 所有依赖都已安装
✓ 测试图片存在
✓ 测试框架验证成功
```

### 2. 查看支持的算法

```bash
python main.py --list-algorithms
```

输出：
```
支持的算法:
  - water_level
```

### 3. 运行第一个测试

```bash
python main.py --test --algorithm water_level
```

## 详细使用方法

### 命令行选项

#### 基本测试命令

```bash
# 运行指定算法的所有测试
python main.py --test --algorithm <算法名>

# 运行特定测试用例
python main.py --test --algorithm <算法名> --case <用例名>

# 运行所有算法测试
python main.py --test --all-tests
```

#### 信息查询命令

```bash
# 列出支持的算法
python main.py --list-algorithms

# 列出指定算法的测试用例
python main.py --list-cases <算法名>
```

#### 报告格式选项

```bash
# 生成HTML报告 (默认)
python main.py --test --algorithm water_level --report-format html

# 生成YAML报告
python main.py --test --algorithm water_level --report-format yaml

# 生成JSON报告
python main.py --test --algorithm water_level --report-format json

# 不生成报告
python main.py --test --algorithm water_level --no-report
```

#### 调试选项

```bash
# 启用调试模式
python main.py --test --algorithm water_level --debug
```

### 水位识别测试

#### 测试用例配置

水位识别测试用例配置在 `data/test_cases/water_level_test_cases.yaml`：

```yaml
test_cases:
  - name: "water_level_case_1"
    description: "标准水位测试 - 1.9米水深"
    image_path: "data/temp/water_level.jpg"
    expected_results:
      water_depth_m: 1.9
      ruler_top_y: 1520
      water_surface_y: 4000
      tolerance:
        water_depth_m: 0.1    # 允许10cm误差
        ruler_top_y: 20       # 允许20像素误差
        water_surface_y: 30   # 允许30像素误差
    test_parameters:
      method: "transparency"
      roi: null
      brightness_thresh: 120
```

#### 运行水位识别测试

```bash
# 运行所有水位识别测试
python main.py --test --algorithm water_level

# 运行特定测试用例
python main.py --test --algorithm water_level --case water_level_case_1
python main.py --test --algorithm water_level --case water_level_case_2

# 运行多个特定测试用例
python main.py --test --algorithm water_level --case water_level_case_1 --case water_level_case_2
```

#### 测试结果解读

测试完成后会显示：

```
=== water_level 测试完成 ===
测试用例: 2
通过: 0
失败: 2
错误: 0
成功率: 0.0%
平均执行时间: 0.24 秒
状态: FAILED
测试报告: /path/to/report.html
详细结果: /path/to/results.yaml
```

- **通过**: 在容差范围内的测试用例数
- **失败**: 超出容差范围的测试用例数
- **错误**: 执行出错的测试用例数
- **成功率**: 通过的测试用例占总数的百分比

## 输出文件说明

### 目录结构

```
data/test_results/
├── water_level_test_results_20250822_143906.yaml    # 详细测试结果
├── water_level_test_report_20250822_143906.html     # HTML测试报告
├── water_level_case_1_visualization.jpg             # 测试用例1可视化
└── water_level_case_2_visualization.jpg             # 测试用例2可视化
```

### 测试结果文件 (YAML)

包含每个测试用例的详细结果：

```yaml
test_results:
- case_name: water_level_case_1
  status: FAILED
  execution_time: 0.456
  actual_results:
    water_depth_m: 2.086
    water_surface_y: 2914
    ruler_top_y: 0
  expected_results:
    water_depth_m: 1.9
    water_surface_y: 4000
    ruler_top_y: 1520
  errors:
    water_depth_m: 0.186
    water_surface_y: -1086
    ruler_top_y: -1520
  within_tolerance: false
```

### HTML测试报告

包含：
- 测试摘要和统计图表
- 每个测试用例的详细结果
- 期望值vs实际值对比表
- 可视化结果图片
- 误差分析

### 可视化图片

自动生成的可视化图片包含：
- 原始测试图片
- 期望的水面线和标尺位置 (绿色)
- 实际检测的水面线和标尺位置 (蓝色)
- 测试结果信息和误差数据

## 添加新的测试用例

### 1. 准备测试图片

将测试图片放在 `data/temp/` 目录下。

### 2. 更新测试用例配置

编辑 `data/test_cases/water_level_test_cases.yaml`，添加新的测试用例：

```yaml
test_cases:
  - name: "water_level_case_3"
    description: "新的测试用例描述"
    image_path: "data/temp/new_test_image.jpg"
    expected_results:
      water_depth_m: 2.5
      ruler_top_y: 200
      water_surface_y: 1800
      tolerance:
        water_depth_m: 0.1
        ruler_top_y: 20
        water_surface_y: 30
    test_parameters:
      method: "transparency"
      roi: null
      brightness_thresh: 120
    notes: "测试用例的备注信息"
```

### 3. 运行新的测试用例

```bash
python main.py --test --algorithm water_level --case water_level_case_3
```

## 故障排除

### 常见问题

1. **测试图片不存在**
   ```
   ✗ 测试图片不存在: data/temp/water_level.jpg
   ```
   解决方案: 确保测试图片存在于指定路径

2. **导入错误**
   ```
   ImportError: No module named 'cv2'
   ```
   解决方案: 安装缺失的依赖包
   ```bash
   pip install opencv-python numpy pyyaml
   ```

3. **测试全部失败**
   - 当前使用的是简化算法，精度有限
   - 这是预期的，需要后续集成完整的算法

### 调试技巧

1. **启用调试模式**
   ```bash
   python main.py --test --algorithm water_level --debug
   ```

2. **查看详细日志**
   日志文件位置: `data/logs/app.log`

3. **检查可视化结果**
   查看生成的可视化图片了解算法检测情况

## 扩展测试框架

### 添加新算法测试器

1. 创建新的测试器类继承 `AlgorithmTester`
2. 实现必需的抽象方法
3. 在 `TestRunner` 中注册新的测试器
4. 创建对应的测试用例配置文件

### 自定义报告格式

1. 在 `ReportGenerator` 中添加新的报告生成方法
2. 更新命令行参数支持新格式
3. 实现自定义的报告模板

## 演示脚本

运行演示脚本了解测试框架的完整功能：

```bash
python demo_testing.py
```

这个脚本会引导你了解测试框架的各种功能和使用方法。
