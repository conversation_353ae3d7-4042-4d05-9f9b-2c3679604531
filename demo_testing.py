#!/usr/bin/env python3
"""
VR算法测试框架演示脚本

展示如何使用测试框架进行算法测试和验证。
"""

import sys
import time
from pathlib import Path

def print_header(title):
    """打印标题"""
    print(f"\n{'='*60}")
    print(f" {title}")
    print(f"{'='*60}")

def print_step(step, description):
    """打印步骤"""
    print(f"\n[步骤 {step}] {description}")
    print("-" * 40)

def run_command_demo(command, description):
    """演示命令运行"""
    print(f"\n💡 {description}")
    print(f"命令: {command}")
    print("执行中...")
    time.sleep(1)  # 模拟执行时间

def main():
    """主演示函数"""
    print_header("VR算法测试框架演示")
    
    print("""
🎯 本演示将展示如何使用VR算法测试框架进行水位识别算法的测试和验证。

📋 测试数据:
  - 图片1: data/temp/water_level.jpg (实际水深: 1.9m)
  - 图片2: data/temp/water_level2.jpg (实际水深: 2.3m)

🔧 测试框架功能:
  - 自动化算法测试
  - 结果对比和误差分析
  - 可视化结果生成
  - 详细测试报告
""")
    
    input("按回车键开始演示...")
    
    # 步骤1: 查看支持的算法
    print_step(1, "查看支持的算法")
    run_command_demo(
        "python main.py --list-algorithms",
        "列出测试框架支持的所有算法"
    )
    print("✓ 当前支持: water_level (水位识别)")
    
    # 步骤2: 查看测试用例
    print_step(2, "查看水位识别的测试用例")
    run_command_demo(
        "python main.py --list-cases water_level",
        "列出水位识别算法的所有测试用例"
    )
    print("✓ 测试用例:")
    print("  - water_level_case_1: 1.9米水深测试")
    print("  - water_level_case_2: 2.3米水深测试")
    
    # 步骤3: 运行单个测试用例
    print_step(3, "运行单个测试用例")
    run_command_demo(
        "python main.py --test --algorithm water_level --case water_level_case_1",
        "运行第一个测试用例"
    )
    print("✓ 测试结果:")
    print("  - 算法执行时间: ~0.5秒")
    print("  - 生成可视化图片")
    print("  - 误差分析报告")
    
    # 步骤4: 运行完整测试
    print_step(4, "运行完整的水位识别测试")
    run_command_demo(
        "python main.py --test --algorithm water_level",
        "运行所有水位识别测试用例"
    )
    print("✓ 完整测试结果:")
    print("  - 测试用例: 2个")
    print("  - 通过: 0个 (简化算法精度有限)")
    print("  - 失败: 2个")
    print("  - 成功率: 0% (需要改进算法)")
    
    # 步骤5: 生成测试报告
    print_step(5, "生成详细测试报告")
    run_command_demo(
        "python main.py --test --algorithm water_level --report-format html",
        "生成HTML格式的详细测试报告"
    )
    print("✓ 报告内容:")
    print("  - 测试摘要和统计")
    print("  - 每个测试用例的详细结果")
    print("  - 期望值vs实际值对比表")
    print("  - 可视化结果图片")
    print("  - 误差分析")
    
    # 步骤6: 查看输出文件
    print_step(6, "查看生成的文件")
    print("📁 测试输出文件位置:")
    print("  - 测试结果: data/test_results/water_level_test_results_*.yaml")
    print("  - HTML报告: data/test_results/water_level_test_report_*.html")
    print("  - 可视化图片: data/test_results/*_visualization.jpg")
    
    # 步骤7: 实际运行演示
    print_step(7, "实际运行测试 (可选)")
    choice = input("是否要实际运行一次测试? (y/n): ").lower().strip()
    
    if choice == 'y':
        print("\n🚀 正在运行实际测试...")
        import subprocess
        try:
            result = subprocess.run([
                sys.executable, "main.py", "--test", "--algorithm", "water_level"
            ], capture_output=True, text=True, cwd=".")
            
            print("✅ 测试执行完成!")
            print("\n📊 测试输出:")
            print(result.stdout)
            
            if result.stderr:
                print("\n⚠️  警告/错误信息:")
                print(result.stderr)
                
            # 检查生成的文件
            test_results_dir = Path("data/test_results")
            if test_results_dir.exists():
                html_files = list(test_results_dir.glob("water_level_test_report_*.html"))
                if html_files:
                    latest_report = max(html_files, key=lambda x: x.stat().st_mtime)
                    print(f"\n📋 最新测试报告: {latest_report}")
                    print("   可以在浏览器中打开查看详细结果")
                    
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
    
    # 总结
    print_header("演示总结")
    print("""
🎉 VR算法测试框架演示完成!

✅ 已实现的功能:
  - 独立的算法测试入口
  - 标准化的测试用例配置
  - 自动化的结果对比和验证
  - 详细的可视化测试报告
  - 灵活的命令行接口

🔧 当前状态:
  - 测试框架: ✅ 完全可用
  - 水位识别算法: ⚠️  简化版本 (精度有限)
  - 测试用例: ✅ 已配置用户提供的数据
  - 报告生成: ✅ HTML/YAML/JSON格式

🚀 下一步改进:
  1. 集成完整的水位识别算法
  2. 添加流速识别和异常检测测试
  3. 优化算法参数和精度
  4. 增强测试报告的可视化

📖 使用文档:
  - 技术设计: .augment/docs/technical_design.md
  - 实施报告: .augment/docs/testing_framework_implementation.md
""")

if __name__ == "__main__":
    main()
